#!/usr/bin/env python3
"""
Script para migrar datos faltantes del S1 al S2
Migra datos desde MySQL 5.7 (s2.gmetrics.cl) hacia MySQL 8.1 (Azure)
"""

import mysql.connector
import logging
from datetime import datetime
import sys
import argparse
from typing import List, Dict, Any, Optional

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuración de conexiones
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4',
    'autocommit': False
}

S2_CONFIG = {
    'host': 'coreprodlegacy.mysql.database.azure.com',
    'port': 3306,
    'user': 'adminlegacydev',
    'password': 'fvb8LmY347ToNvsq2sPqgD0E0JBnXUX9',
    'database': 'dbgmprod',
    'charset': 'utf8mb4',
    'autocommit': False,
    'ssl_disabled': False
}

class DatabaseMigrator:
    def __init__(self):
        self.s1_conn = None
        self.s2_conn = None
        self.batch_size = 1000
        
    def connect_databases(self):
        """Establece conexiones a ambas bases de datos"""
        try:
            logger.info("Conectando a S1 (origen)...")
            self.s1_conn = mysql.connector.connect(**S1_CONFIG)
            logger.info("✓ Conexión S1 establecida")
            
            logger.info("Conectando a S2 (destino)...")
            self.s2_conn = mysql.connector.connect(**S2_CONFIG)
            logger.info("✓ Conexión S2 establecida")
            
        except mysql.connector.Error as e:
            logger.error(f"Error conectando a las bases de datos: {e}")
            raise
    
    def close_connections(self):
        """Cierra las conexiones a las bases de datos"""
        if self.s1_conn:
            self.s1_conn.close()
            logger.info("Conexión S1 cerrada")
        if self.s2_conn:
            self.s2_conn.close()
            logger.info("Conexión S2 cerrada")
    
    def get_table_list(self) -> List[str]:
        """Obtiene la lista de tablas de S1 (excluyendo vistas)"""
        cursor = self.s1_conn.cursor()
        cursor.execute("""
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """, (S1_CONFIG['database'],))
        tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        logger.info(f"Encontradas {len(tables)} tablas (excluyendo vistas) en S1")

        # También reportar cuántas vistas hay para información
        cursor = self.s1_conn.cursor()
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'VIEW'
        """, (S1_CONFIG['database'],))
        view_count = cursor.fetchone()[0]
        cursor.close()

        if view_count > 0:
            logger.info(f"Se encontraron {view_count} vistas que serán omitidas de la migración")

        return tables

    def get_view_list(self) -> List[str]:
        """Obtiene la lista de vistas de S1 (para información/debug)"""
        cursor = self.s1_conn.cursor()
        cursor.execute("""
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'VIEW'
            ORDER BY TABLE_NAME
        """, (S1_CONFIG['database'],))
        views = [view[0] for view in cursor.fetchall()]
        cursor.close()
        return views

    def get_table_structure(self, table_name: str) -> str:
        """Obtiene la estructura de una tabla"""
        cursor = self.s1_conn.cursor()
        cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
        result = cursor.fetchone()
        cursor.close()
        return result[1] if result else ""
    
    def table_exists_in_s2(self, table_name: str) -> bool:
        """Verifica si una tabla existe en S2"""
        cursor = self.s2_conn.cursor()
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s
        """, (S2_CONFIG['database'], table_name))
        exists = cursor.fetchone()[0] > 0
        cursor.close()
        return exists
    
    def get_row_count(self, table_name: str, connection) -> int:
        """Obtiene el número de filas de una tabla"""
        cursor = connection.cursor(buffered=True)
        try:
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            count = cursor.fetchone()[0]
            return count
        finally:
            cursor.close()
    
    def get_primary_key(self, table_name: str) -> Optional[str]:
        """Obtiene la clave primaria de una tabla"""
        cursor = self.s1_conn.cursor(buffered=True)
        try:
            cursor.execute("""
                SELECT COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = %s
                AND CONSTRAINT_NAME = 'PRIMARY'
                ORDER BY ORDINAL_POSITION
            """, (S1_CONFIG['database'], table_name))

            pk_columns = [row[0] for row in cursor.fetchall()]
            return pk_columns[0] if pk_columns else None
        finally:
            cursor.close()
    
    def migrate_table_data(self, table_name: str, dry_run: bool = False):
        """Migra los datos de una tabla específica"""
        logger.info(f"Iniciando migración de tabla: {table_name}")
        
        # Verificar si la tabla existe en S2
        if not self.table_exists_in_s2(table_name):
            logger.warning(f"Tabla {table_name} no existe en S2, saltando...")
            return
        
        # Obtener conteos
        s1_count = self.get_row_count(table_name, self.s1_conn)
        s2_count = self.get_row_count(table_name, self.s2_conn)
        
        logger.info(f"Tabla {table_name}: S1={s1_count} filas, S2={s2_count} filas")
        
        if s1_count <= s2_count:
            logger.info(f"Tabla {table_name} ya está actualizada, saltando...")
            return
        
        # Obtener clave primaria para ordenamiento
        pk_column = self.get_primary_key(table_name)
        order_by = f"ORDER BY `{pk_column}`" if pk_column else ""
        
        # Migrar datos en lotes
        s1_cursor = self.s1_conn.cursor(dictionary=True, buffered=True)
        s2_cursor = self.s2_conn.cursor(buffered=True)

        try:
            # Obtener estructura de columnas
            s1_cursor.execute(f"SELECT * FROM `{table_name}` LIMIT 1")
            rows = s1_cursor.fetchall()  # Consumir todos los resultados
            if s1_cursor.description:
                columns = [desc[0] for desc in s1_cursor.description]
                
                offset = s2_count  # Comenzar desde donde S2 se quedó
                migrated_rows = 0
                
                while True:
                    # Leer lote de S1
                    query = f"""
                        SELECT * FROM `{table_name}` 
                        {order_by}
                        LIMIT {self.batch_size} OFFSET {offset}
                    """
                    s1_cursor.execute(query)
                    rows = s1_cursor.fetchall()
                    
                    if not rows:
                        break
                    
                    if not dry_run:
                        # Preparar query de inserción
                        placeholders = ', '.join(['%s'] * len(columns))
                        insert_query = f"""
                            INSERT IGNORE INTO `{table_name}` 
                            ({', '.join([f'`{col}`' for col in columns])}) 
                            VALUES ({placeholders})
                        """
                        
                        # Insertar lote en S2
                        values = [tuple(row[col] for col in columns) for row in rows]
                        s2_cursor.executemany(insert_query, values)
                        self.s2_conn.commit()
                    
                    migrated_rows += len(rows)
                    offset += len(rows)
                    
                    logger.info(f"Tabla {table_name}: migradas {migrated_rows} filas...")
                    
                    if len(rows) < self.batch_size:
                        break
                
                logger.info(f"✓ Tabla {table_name}: migración completada ({migrated_rows} filas)")
            
        except Exception as e:
            logger.error(f"Error migrando tabla {table_name}: {e}")
            self.s2_conn.rollback()
            raise
        finally:
            s1_cursor.close()
            s2_cursor.close()
    
    def run_migration(self, tables: List[str] = None, dry_run: bool = False):
        """Ejecuta la migración completa"""
        try:
            self.connect_databases()
            
            if not tables:
                tables = self.get_table_list()
            
            logger.info(f"Iniciando migración de {len(tables)} tablas...")
            logger.info(f"Modo: {'DRY RUN' if dry_run else 'EJECUCIÓN REAL'}")
            
            for table in tables:
                try:
                    self.migrate_table_data(table, dry_run)
                except Exception as e:
                    logger.error(f"Error en tabla {table}: {e}")
                    continue
            
            logger.info("Migración completada")
            
        except Exception as e:
            logger.error(f"Error en migración: {e}")
            raise
        finally:
            self.close_connections()

def main():
    parser = argparse.ArgumentParser(description='Migrar datos de S1 a S2')
    parser.add_argument('--tables', nargs='+', help='Tablas específicas a migrar')
    parser.add_argument('--dry-run', action='store_true', help='Ejecutar en modo prueba')
    parser.add_argument('--batch-size', type=int, default=1000, help='Tamaño del lote')
    parser.add_argument('--list-views', action='store_true', help='Listar vistas encontradas y salir')

    args = parser.parse_args()

    migrator = DatabaseMigrator()
    if args.batch_size:
        migrator.batch_size = args.batch_size

    # Si se solicita listar vistas, hacer solo eso
    if args.list_views:
        try:
            migrator.connect_databases()
            views = migrator.get_view_list()
            if views:
                logger.info(f"Vistas encontradas en S1 ({len(views)}):")
                for view in views:
                    logger.info(f"  - {view}")
            else:
                logger.info("No se encontraron vistas en S1")
        except Exception as e:
            logger.error(f"Error listando vistas: {e}")
        finally:
            migrator.close_connections()
        return

    migrator.run_migration(args.tables, args.dry_run)

if __name__ == "__main__":
    main()
