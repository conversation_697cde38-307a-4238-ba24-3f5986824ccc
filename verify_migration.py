#!/usr/bin/env python3
"""
Script simple para verificar el estado de migración de tablas específicas
"""

import mysql.connector
import sys

# Configuración de conexiones
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4'
}

S2_CONFIG = {
    'host': 'coreprodlegacy.mysql.database.azure.com',
    'port': 3306,
    'user': 'adminlegacydev',
    'password': 'fvb8LmY347ToNvsq2sPqgD0E0JBnXUX9',
    'database': 'dbgmprod',
    'charset': 'utf8mb4',
    'ssl_disabled': False
}

def verify_tables(table_names):
    """Verifica el estado de las tablas especificadas"""
    try:
        # Conectar a ambas bases de datos
        s1_conn = mysql.connector.connect(**S1_CONFIG)
        s2_conn = mysql.connector.connect(**S2_CONFIG)
        
        print("Verificación de Migración")
        print("=" * 50)
        
        for table in table_names:
            try:
                # Contar filas en S1
                s1_cursor = s1_conn.cursor()
                s1_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                s1_count = s1_cursor.fetchone()[0]
                s1_cursor.close()
                
                # Contar filas en S2
                s2_cursor = s2_conn.cursor()
                s2_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                s2_count = s2_cursor.fetchone()[0]
                s2_cursor.close()
                
                difference = s1_count - s2_count
                status = "✓ OK" if difference == 0 else f"⚠ PENDIENTE ({difference})"
                
                print(f"{table:30} S1:{s1_count:8} S2:{s2_count:8} {status}")
                
            except Exception as e:
                print(f"{table:30} ERROR: {e}")
        
        s1_conn.close()
        s2_conn.close()
        
    except Exception as e:
        print(f"Error de conexión: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        tables = sys.argv[1:]
    else:
        tables = ['gm_mail_servers', 'gm_programs', 'kiosk_status']
    
    verify_tables(tables)
