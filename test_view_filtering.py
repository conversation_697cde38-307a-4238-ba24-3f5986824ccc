#!/usr/bin/env python3
"""
Script de prueba para verificar que las vistas se filtran correctamente
"""

import mysql.connector
import sys

# Configuración de conexiones (misma que en migrate_s1_to_s2.py)
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4',
    'autocommit': False
}

def test_table_vs_view_filtering():
    """Prueba la diferencia entre SHOW TABLES y filtrar por TABLE_TYPE"""
    try:
        conn = mysql.connector.connect(**S1_CONFIG)
        cursor = conn.cursor()
        
        print("=== COMPARACIÓN: SHOW TABLES vs FILTRADO POR TABLE_TYPE ===\n")
        
        # <PERSON><PERSON>todo anterior (SHOW TABLES - incluye vistas)
        cursor.execute("SHOW TABLES")
        all_objects = [table[0] for table in cursor.fetchall()]
        
        # Método nuevo (solo tablas BASE TABLE)
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """, (S1_CONFIG['database'],))
        only_tables = [table[0] for table in cursor.fetchall()]
        
        # Solo vistas
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_TYPE = 'VIEW'
            ORDER BY TABLE_NAME
        """, (S1_CONFIG['database'],))
        only_views = [view[0] for view in cursor.fetchall()]
        
        print(f"📊 RESULTADOS:")
        print(f"   SHOW TABLES (total):     {len(all_objects)} objetos")
        print(f"   Solo tablas (BASE TABLE): {len(only_tables)} tablas")
        print(f"   Solo vistas (VIEW):       {len(only_views)} vistas")
        print(f"   Verificación: {len(only_tables)} + {len(only_views)} = {len(only_tables) + len(only_views)} (debe ser igual a {len(all_objects)})")
        
        if len(only_views) > 0:
            print(f"\n🔍 VISTAS ENCONTRADAS ({len(only_views)}):")
            for i, view in enumerate(only_views[:10], 1):  # Mostrar solo las primeras 10
                print(f"   {i:2d}. {view}")
            if len(only_views) > 10:
                print(f"   ... y {len(only_views) - 10} más")
        
        # Verificar que no hay objetos perdidos
        missing_objects = set(all_objects) - set(only_tables) - set(only_views)
        if missing_objects:
            print(f"\n⚠️  OBJETOS NO CLASIFICADOS: {missing_objects}")
        else:
            print(f"\n✅ TODOS LOS OBJETOS ESTÁN CORRECTAMENTE CLASIFICADOS")
        
        cursor.close()
        conn.close()
        
        return len(only_views) > 0
        
    except mysql.connector.Error as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def main():
    print("🧪 PRUEBA DE FILTRADO DE VISTAS EN MIGRACIÓN S1→S2\n")
    
    has_views = test_table_vs_view_filtering()
    
    if has_views:
        print(f"\n✅ PROBLEMA IDENTIFICADO Y SOLUCIONADO:")
        print(f"   - El script migrate_s1_to_s2.py ahora filtra correctamente las vistas")
        print(f"   - Solo se migrarán las tablas reales (BASE TABLE)")
        print(f"   - Las vistas se omitirán automáticamente")
        print(f"\n💡 COMANDOS ÚTILES:")
        print(f"   python migrate_s1_to_s2.py --list-views    # Ver qué vistas se omiten")
        print(f"   python migrate_s1_to_s2.py --dry-run       # Probar migración sin cambios")
    else:
        print(f"\n📝 NO SE ENCONTRARON VISTAS EN LA BASE DE DATOS")
        print(f"   - El filtrado sigue siendo útil para futuras vistas")
        print(f"   - El código está preparado para manejar vistas correctamente")

if __name__ == "__main__":
    main()
