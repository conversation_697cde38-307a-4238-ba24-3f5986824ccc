# 🚀 Guía del Menú de Migración S1 → S2

## Inicio R<PERSON>pido

```bash
python migration_menu.py
```

¡Eso es todo! El menú interactivo te guiará a través de todo el proceso.

## 📋 Opciones del Menú Principal

### 1. 🔍 An<PERSON><PERSON><PERSON>
- **Qué hace**: Analiza diferencias entre S1 y S2 usando conteos reales
- **Tiempo**: Más lento pero preciso
- **Cuándo usar**: Primera vez o cuando necesites precisión total
- **Salida**: Reporte CSV detallado + resumen en consola

### 2. ⚡ Aná<PERSON>is Rápido  
- **Qué hace**: Análisis usando estimaciones de `information_schema`
- **Tiempo**: Más rápido pero menos preciso
- **Cuándo usar**: Para verificaciones rápidas o bases de datos grandes
- **Nota**: Puede tener falsos positivos

### 3. 🧪 Simulación de Migración
- **Qué hace**: Ejecuta migración en modo `--dry-run` (sin cambios reales)
- **Tiempo**: Variable según tablas
- **Cuándo usar**: Antes de cualquier migración real para verificar que funciona
- **Seguridad**: 100% seguro, no modifica datos

### 4. 🚀 Migración Real
- **Qué hace**: Ejecuta la migración real con confirmación
- **Tiempo**: Variable según volumen de datos
- **Cuándo usar**: Después de verificar con simulación
- **⚠️ ATENCIÓN**: Modifica la base de datos de destino

### 5. 🔄 Proceso Completo Automático
- **Qué hace**: Ejecuta todo el flujo recomendado:
  1. Análisis inicial
  2. Simulación 
  3. Migración real (con confirmación)
  4. Verificación final
- **Cuándo usar**: Para migración completa sin intervención manual
- **Ventaja**: Proceso guiado paso a paso

### 6. 🔧 Opciones Avanzadas
- Migrar tablas específicas
- Cambiar tamaño de lote
- Listar vistas (debug)
- Debug de conteos

### 7. 📊 Ver Último Reporte
- Abre el archivo CSV del último análisis
- Funciona en macOS, Linux y Windows

### 8. 🧪 Scripts de Verificación
- Ejecuta scripts de debug y verificación
- Útil para troubleshooting

## 🔄 Flujo Recomendado

### Para Primera Migración:
```
1. Ejecutar "Análisis Completo" (opción 1)
2. Revisar reporte generado
3. Ejecutar "Simulación" (opción 3) 
4. Si todo OK → "Migración Real" (opción 4)
5. Ejecutar "Análisis Completo" nuevamente para verificar
```

### Para Migración Automática:
```
1. Ejecutar "Proceso Completo Automático" (opción 5)
2. Seguir las confirmaciones paso a paso
```

### Para Verificaciones Rápidas:
```
1. Ejecutar "Análisis Rápido" (opción 2)
2. Si hay dudas → "Análisis Completo" (opción 1)
```

## 🛡️ Características de Seguridad

### Confirmaciones Múltiples
- Migración real requiere confirmación explícita
- Proceso automático pide confirmación en cada paso crítico

### Modo Dry-Run por Defecto
- Simulaciones nunca modifican datos
- Siempre se recomienda probar antes de migrar

### Validación de Conexiones
- Verifica conectividad antes de comenzar
- Falla rápido si hay problemas de conexión

### Logging Detallado
- Cada operación genera logs detallados
- Archivos de log con timestamp para auditoría

## 🎯 Casos de Uso Específicos

### Migración de Tablas Específicas
```
1. Ir a "Opciones Avanzadas" (opción 6)
2. Seleccionar "Migrar tablas específicas"
3. Ingresar nombres separados por espacio
4. Elegir simulación o migración real
```

### Debug de Problemas de Conteo
```
1. Ir a "Scripts de Verificación" (opción 8)
2. Seleccionar "Debug de conteos específicos"
3. Ingresar nombres de tablas problemáticas
```

### Cambiar Tamaño de Lote
```
1. Ir a "Opciones Avanzadas" (opción 6)
2. Seleccionar "Cambiar tamaño de lote"
3. Ingresar nuevo valor (default: 1000)
```

## 📊 Interpretación de Resultados

### Estado de Tablas
- **✅ OK**: Tablas sincronizadas (mismo número de filas)
- **⚠️ PENDIENTE**: S1 tiene más filas que S2
- **🔴 EXTRA_S2**: S2 tiene más filas que S1 (revisar)
- **❌ ERROR**: Error al acceder a la tabla

### Archivos Generados
- `migration_report_YYYYMMDD_HHMMSS.csv`: Reporte detallado
- `migration_YYYYMMDD_HHMMSS.log`: Log de migración
- Archivos se generan automáticamente con timestamp

## 🚨 Troubleshooting

### "No hay reportes disponibles"
- Ejecuta un análisis primero (opción 1 o 2)

### "Error de conexión"
- Verifica credenciales en los archivos de configuración
- Confirma conectividad de red

### "Tablas iguales aparecen como pendientes"
- Usa "Análisis Completo" en lugar de "Análisis Rápido"
- Ejecuta debug de conteos para tablas específicas

### "Migración muy lenta"
- Reduce el tamaño de lote en opciones avanzadas
- Migra tablas grandes individualmente

## 💡 Tips y Mejores Prácticas

1. **Siempre simula primero**: Nunca ejecutes migración real sin simular
2. **Revisa los logs**: Los archivos de log contienen información detallada
3. **Migra en horarios de bajo tráfico**: Para minimizar impacto
4. **Haz backup antes**: Aunque el script usa `INSERT IGNORE`, siempre es buena práctica
5. **Monitorea el progreso**: Los logs muestran progreso en tiempo real
6. **Usa análisis completo para decisiones críticas**: Las estimaciones pueden ser inexactas

## 🔗 Archivos Relacionados

- `migration_analysis.py`: Script de análisis individual
- `migrate_s1_to_s2.py`: Script de migración individual  
- `verify_fix.py`: Verificación de correcciones
- `debug_count_issue.py`: Debug de problemas de conteo
- `README.md`: Documentación completa del proyecto
