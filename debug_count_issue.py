#!/usr/bin/env python3
"""
Script para debuggear el problema de conteos incorrectos
Compara TABLE_ROWS vs COUNT(*) real
"""

import mysql.connector
import sys

# Configuración de conexiones
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4',
    'autocommit': False
}

S2_CONFIG = {
    'host': 'coreprodlegacy.mysql.database.azure.com',
    'port': 3306,
    'user': 'adminlegacydev',
    'password': 'fvb8LmY347ToNvsq2sPqgD0E0JBnXUX9',
    'database': 'dbgmprod',
    'charset': 'utf8mb4',
    'autocommit': False,
    'ssl_disabled': False
}

def compare_counting_methods(table_names=None):
    """Compara TABLE_ROWS vs COUNT(*) real para identificar discrepancias"""
    try:
        s1_conn = mysql.connector.connect(**S1_CONFIG)
        s2_conn = mysql.connector.connect(**S2_CONFIG)
        
        s1_cursor = s1_conn.cursor()
        s2_cursor = s2_conn.cursor()
        
        if not table_names:
            # Obtener algunas tablas de ejemplo
            s1_cursor.execute("""
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
                LIMIT 10
            """, (S1_CONFIG['database'],))
            table_names = [row[0] for row in s1_cursor.fetchall()]
        
        print("🔍 COMPARACIÓN: TABLE_ROWS vs COUNT(*) REAL")
        print("=" * 80)
        print(f"{'TABLA':<25} {'S1_ESTIMATE':<12} {'S1_REAL':<10} {'S2_ESTIMATE':<12} {'S2_REAL':<10} {'DIFERENCIA':<12}")
        print("-" * 80)
        
        discrepancies_found = False
        
        for table in table_names:
            try:
                # S1 - Estimación de information_schema
                s1_cursor.execute("""
                    SELECT TABLE_ROWS 
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                """, (S1_CONFIG['database'], table))
                s1_estimate = s1_cursor.fetchone()
                s1_estimate = s1_estimate[0] if s1_estimate else 0
                
                # S1 - Conteo real
                s1_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                s1_real = s1_cursor.fetchone()[0]
                
                # S2 - Estimación de information_schema
                s2_cursor.execute("""
                    SELECT TABLE_ROWS 
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                """, (S2_CONFIG['database'], table))
                s2_estimate = s2_cursor.fetchone()
                s2_estimate = s2_estimate[0] if s2_estimate else 0
                
                # S2 - Conteo real
                s2_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                s2_real = s2_cursor.fetchone()[0]
                
                # Diferencia real
                real_diff = s1_real - s2_real
                estimate_diff = s1_estimate - s2_estimate
                
                print(f"{table:<25} {s1_estimate:<12} {s1_real:<10} {s2_estimate:<12} {s2_real:<10} {real_diff:<12}")
                
                # Detectar discrepancias
                if s1_estimate != s1_real or s2_estimate != s2_real:
                    discrepancies_found = True
                    print(f"  ⚠️  DISCREPANCIA: S1 est={s1_estimate} real={s1_real}, S2 est={s2_estimate} real={s2_real}")
                
                if real_diff == 0 and estimate_diff != 0:
                    print(f"  🐛 FALSO POSITIVO: Tablas iguales pero estimación dice diferencia de {estimate_diff}")
                
            except Exception as e:
                print(f"{table:<25} ERROR: {e}")
        
        print("-" * 80)
        
        if discrepancies_found:
            print("\n❌ PROBLEMA IDENTIFICADO:")
            print("   - information_schema.TABLE_ROWS da estimaciones incorrectas")
            print("   - Esto causa falsos positivos en el análisis de migración")
            print("   - SOLUCIÓN: Usar siempre COUNT(*) real para comparaciones")
        else:
            print("\n✅ No se encontraron discrepancias en estas tablas")
        
        s1_cursor.close()
        s2_cursor.close()
        s1_conn.close()
        s2_conn.close()
        
        return discrepancies_found
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🧪 DEBUG: Problema de conteos incorrectos en migration_analysis.py\n")
    
    if len(sys.argv) > 1:
        tables = sys.argv[1:]
        print(f"Analizando tablas específicas: {', '.join(tables)}")
    else:
        tables = None
        print("Analizando primeras 10 tablas encontradas...")
    
    print()
    has_discrepancies = compare_counting_methods(tables)
    
    if has_discrepancies:
        print(f"\n💡 RECOMENDACIÓN:")
        print(f"   Modificar migration_analysis.py para usar COUNT(*) real")
        print(f"   en lugar de information_schema.TABLE_ROWS")

if __name__ == "__main__":
    main()
