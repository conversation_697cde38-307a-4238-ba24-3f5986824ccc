#!/usr/bin/env python3
"""
Menú principal para el proceso de migración S1 → S2
Orquesta todos los scripts de migración de manera interactiva
"""

import os
import sys
import subprocess
import time
from datetime import datetime
import mysql.connector

# Configuración de conexiones para verificaciones rápidas
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4',
    'autocommit': False
}

S2_CONFIG = {
    'host': 'coreprodlegacy.mysql.database.azure.com',
    'port': 3306,
    'user': 'adminlegacydev',
    'password': 'fvb8LmY347ToNvsq2sPqgD0E0JBnXUX9',
    'database': 'dbgmprod',
    'charset': 'utf8mb4',
    'autocommit': False,
    'ssl_disabled': False
}

class MigrationMenu:
    def __init__(self):
        self.last_analysis_file = None
        self.pending_tables = []
        
    def clear_screen(self):
        """Limpia la pantalla"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Imprime el header del menú"""
        print("=" * 70)
        print("🚀 MIGRACIÓN S1 → S2 - MENÚ PRINCIPAL")
        print("=" * 70)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 S1: {S1_CONFIG['host']} → S2: {S2_CONFIG['host']}")
        print("=" * 70)

    def show_quick_status(self):
        """Muestra un resumen rápido del estado"""
        print("\n📊 ESTADO RÁPIDO:")

        # Verificar si hay reportes recientes
        import glob
        reports = glob.glob("migration_report_*.csv")
        if reports:
            latest_report = max(reports, key=os.path.getctime)
            mod_time = datetime.fromtimestamp(os.path.getmtime(latest_report))
            time_diff = datetime.now() - mod_time

            if time_diff.total_seconds() < 3600:  # Menos de 1 hora
                print(f"✅ Reporte reciente disponible: {latest_report}")
                print(f"   📅 Generado hace {int(time_diff.total_seconds()/60)} minutos")
            else:
                print(f"⚠️  Último reporte: {latest_report}")
                print(f"   📅 Generado hace {time_diff.days} días" if time_diff.days > 0 else f"   📅 Generado hace {int(time_diff.total_seconds()/3600)} horas")
        else:
            print("❌ No hay reportes de análisis disponibles")
            print("   💡 Recomendación: Ejecuta un análisis primero (opción 1 o 2)")

        print("-" * 50)
    
    def test_connections(self):
        """Prueba las conexiones a ambas bases de datos"""
        print("🔍 Verificando conexiones...")
        
        try:
            # Probar S1
            s1_conn = mysql.connector.connect(**S1_CONFIG)
            s1_conn.close()
            print("✅ S1 (origen): Conexión exitosa")
            
            # Probar S2
            s2_conn = mysql.connector.connect(**S2_CONFIG)
            s2_conn.close()
            print("✅ S2 (destino): Conexión exitosa")
            
            return True
            
        except Exception as e:
            print(f"❌ Error de conexión: {e}")
            return False
    
    def run_analysis(self, fast_mode=False):
        """Ejecuta el análisis de migración"""
        print(f"\n🔍 Ejecutando análisis {'rápido' if fast_mode else 'completo'}...")
        
        cmd = ['python', 'migration_analysis.py']
        if fast_mode:
            cmd.append('--fast')
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Análisis completado exitosamente")
                
                # Buscar archivo de reporte generado
                import glob
                reports = glob.glob("migration_report_*.csv")
                if reports:
                    self.last_analysis_file = max(reports, key=os.path.getctime)
                    print(f"📄 Reporte guardado: {self.last_analysis_file}")
                
                # Mostrar salida del análisis
                print("\n" + "="*50)
                print(result.stdout)
                print("="*50)
                
                return True
            else:
                print(f"❌ Error en análisis: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error ejecutando análisis: {e}")
            return False
    
    def run_migration(self, tables=None, dry_run=True, batch_size=1000):
        """Ejecuta la migración"""
        mode_text = "SIMULACIÓN" if dry_run else "MIGRACIÓN REAL"
        tables_text = f"tablas específicas: {', '.join(tables)}" if tables else "todas las tablas pendientes"
        
        print(f"\n🚀 Ejecutando {mode_text} para {tables_text}...")
        
        cmd = ['python', 'migrate_s1_to_s2.py']
        
        if dry_run:
            cmd.append('--dry-run')
        
        if tables:
            cmd.extend(['--tables'] + tables)
        
        if batch_size != 1000:
            cmd.extend(['--batch-size', str(batch_size)])
        
        try:
            print(f"💻 Comando: {' '.join(cmd)}")
            print("⏳ Ejecutando... (esto puede tomar tiempo)")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {mode_text} completada exitosamente")
                print("\n" + "="*50)
                print(result.stdout)
                print("="*50)
                return True
            else:
                print(f"❌ Error en {mode_text.lower()}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error ejecutando migración: {e}")
            return False
    
    def show_main_menu(self):
        """Muestra el menú principal"""
        print("\n📋 OPCIONES DISPONIBLES:")
        print("1. 🔍 Análisis completo (preciso, más lento)")
        print("2. ⚡ Análisis rápido (estimaciones, más rápido)")
        print("3. 🧪 Simulación de migración (dry-run)")
        print("4. 🚀 Migración real")
        print("5. 🔄 Proceso completo automático")
        print("6. 🔧 Opciones avanzadas")
        print("7. 📊 Ver último reporte")
        print("8. 🧪 Scripts de verificación")
        print("9. ❌ Salir")
        print("-" * 50)
    
    def show_advanced_menu(self):
        """Muestra el menú de opciones avanzadas"""
        print("\n🔧 OPCIONES AVANZADAS:")
        print("1. 🎯 Migrar tablas específicas")
        print("2. ⚙️  Cambiar tamaño de lote")
        print("3. 👁️  Listar vistas (debug)")
        print("4. 🔍 Debug de conteos")
        print("5. 🔙 Volver al menú principal")
        print("-" * 50)
    
    def get_user_input(self, prompt, valid_options=None):
        """Obtiene input del usuario con validación"""
        while True:
            try:
                response = input(f"{prompt}: ").strip()
                
                if valid_options and response not in valid_options:
                    print(f"❌ Opción inválida. Opciones válidas: {', '.join(valid_options)}")
                    continue
                
                return response
                
            except KeyboardInterrupt:
                print("\n👋 Saliendo...")
                sys.exit(0)

    def run_complete_process(self):
        """Ejecuta el proceso completo: análisis → simulación → migración → verificación"""
        print("\n🔄 PROCESO COMPLETO AUTOMÁTICO")
        print("=" * 50)
        print("Este proceso ejecutará:")
        print("1. 🔍 Análisis inicial")
        print("2. 🧪 Simulación de migración")
        print("3. 🚀 Migración real (con confirmación)")
        print("4. ✅ Análisis final de verificación")
        print("=" * 50)

        confirm = self.get_user_input("¿Continuar con el proceso completo? (si/no)", ["si", "no"])
        if confirm != "si":
            print("❌ Proceso cancelado")
            return

        # Paso 1: Análisis inicial
        print("\n📍 PASO 1/4: Análisis inicial")
        if not self.run_analysis(fast_mode=False):
            print("❌ Error en análisis inicial. Proceso abortado.")
            return

        # Paso 2: Simulación
        print("\n📍 PASO 2/4: Simulación de migración")
        if not self.run_migration(dry_run=True):
            print("❌ Error en simulación. Proceso abortado.")
            return

        # Paso 3: Confirmación para migración real
        print("\n📍 PASO 3/4: Migración real")
        print("⚠️  ATENCIÓN: Se procederá con la migración real")
        confirm_real = self.get_user_input("¿Ejecutar migración real? (si/no)", ["si", "no"])

        if confirm_real == "si":
            if not self.run_migration(dry_run=False):
                print("❌ Error en migración real.")
                return
        else:
            print("⏸️  Migración real omitida por el usuario")
            return

        # Paso 4: Verificación final
        print("\n📍 PASO 4/4: Verificación final")
        if self.run_analysis(fast_mode=False):
            print("\n🎉 PROCESO COMPLETO FINALIZADO EXITOSAMENTE")
            print("✅ Revisa el último reporte para confirmar que todo está sincronizado")
        else:
            print("⚠️  Proceso completado pero hubo errores en la verificación final")

        print("\n" + "="*60)
        print("📋 RESUMEN DEL PROCESO:")
        print("1. ✅ Análisis inicial completado")
        print("2. ✅ Simulación completada")
        print("3. ✅ Migración real completada" if confirm_real == "si" else "3. ⏸️  Migración real omitida")
        print("4. ✅ Verificación final completada")
        print("="*60)
    
    def run_verification_scripts(self):
        """Ejecuta scripts de verificación"""
        print("\n🧪 SCRIPTS DE VERIFICACIÓN:")
        print("1. Verificar correcciones aplicadas")
        print("2. Debug de conteos específicos")
        print("3. Volver")
        
        choice = self.get_user_input("Selecciona opción", ["1", "2", "3"])
        
        if choice == "1":
            print("\n🔍 Ejecutando verificación de correcciones...")
            subprocess.run(['python', 'verify_fix.py'])
        elif choice == "2":
            tables = input("Ingresa nombres de tablas separadas por espacio (o Enter para usar ejemplos): ").strip()
            cmd = ['python', 'debug_count_issue.py']
            if tables:
                cmd.extend(tables.split())
            subprocess.run(cmd)
    
    def run(self):
        """Ejecuta el menú principal"""
        self.clear_screen()
        self.print_header()
        
        # Verificar conexiones al inicio
        if not self.test_connections():
            print("\n❌ No se puede continuar sin conexiones válidas")
            input("Presiona Enter para salir...")
            return

        # Mostrar estado rápido
        self.show_quick_status()
        
        while True:
            self.show_main_menu()
            choice = self.get_user_input("Selecciona una opción",
                                       ["1", "2", "3", "4", "5", "6", "7", "8", "9"])
            
            if choice == "1":
                # Análisis completo
                self.run_analysis(fast_mode=False)
                input("\nPresiona Enter para continuar...")
                
            elif choice == "2":
                # Análisis rápido
                self.run_analysis(fast_mode=True)
                input("\nPresiona Enter para continuar...")
                
            elif choice == "3":
                # Simulación de migración
                print("\n🧪 SIMULACIÓN DE MIGRACIÓN")
                tables_input = input("Tablas específicas (separadas por espacio, o Enter para todas): ").strip()
                tables = tables_input.split() if tables_input else None
                
                self.run_migration(tables=tables, dry_run=True)
                input("\nPresiona Enter para continuar...")
                
            elif choice == "4":
                # Migración real
                print("\n⚠️  MIGRACIÓN REAL - ESTO MODIFICARÁ LA BASE DE DATOS")
                confirm = self.get_user_input("¿Estás seguro? (si/no)", ["si", "no"])
                
                if confirm == "si":
                    tables_input = input("Tablas específicas (separadas por espacio, o Enter para todas): ").strip()
                    tables = tables_input.split() if tables_input else None
                    
                    self.run_migration(tables=tables, dry_run=False)
                else:
                    print("❌ Migración cancelada")
                
                input("\nPresiona Enter para continuar...")
                
            elif choice == "5":
                # Proceso completo automático
                self.run_complete_process()
                input("\nPresiona Enter para continuar...")

            elif choice == "6":
                # Opciones avanzadas
                self.handle_advanced_menu()

            elif choice == "7":
                # Ver último reporte
                if self.last_analysis_file and os.path.exists(self.last_analysis_file):
                    print(f"\n📄 Abriendo {self.last_analysis_file}...")
                    if sys.platform == "darwin":  # macOS
                        subprocess.run(['open', self.last_analysis_file])
                    elif sys.platform == "linux":
                        subprocess.run(['xdg-open', self.last_analysis_file])
                    else:
                        print(f"Archivo ubicado en: {os.path.abspath(self.last_analysis_file)}")
                else:
                    print("❌ No hay reportes disponibles. Ejecuta un análisis primero.")
                
                input("\nPresiona Enter para continuar...")

            elif choice == "8":
                # Scripts de verificación
                self.run_verification_scripts()
                input("\nPresiona Enter para continuar...")

            elif choice == "9":
                # Salir
                print("\n👋 ¡Hasta luego!")
                break
    
    def handle_advanced_menu(self):
        """Maneja el menú avanzado"""
        while True:
            self.show_advanced_menu()
            choice = self.get_user_input("Selecciona opción", ["1", "2", "3", "4", "5"])
            
            if choice == "1":
                # Migrar tablas específicas
                tables = input("Ingresa nombres de tablas separadas por espacio: ").strip().split()
                if tables:
                    dry_run = self.get_user_input("¿Simulación? (si/no)", ["si", "no"]) == "si"
                    self.run_migration(tables=tables, dry_run=dry_run)
                input("\nPresiona Enter para continuar...")
                
            elif choice == "2":
                # Cambiar tamaño de lote
                try:
                    batch_size = int(input("Nuevo tamaño de lote (actual: 1000): "))
                    print(f"✅ Tamaño de lote configurado a: {batch_size}")
                except ValueError:
                    print("❌ Valor inválido")
                input("\nPresiona Enter para continuar...")
                
            elif choice == "3":
                # Listar vistas
                subprocess.run(['python', 'migrate_s1_to_s2.py', '--list-views'])
                input("\nPresiona Enter para continuar...")
                
            elif choice == "4":
                # Debug de conteos
                tables = input("Tablas para debug (separadas por espacio): ").strip()
                cmd = ['python', 'migration_analysis.py', '--debug-counts']
                if tables:
                    cmd.extend(tables.split())
                subprocess.run(cmd)
                input("\nPresiona Enter para continuar...")
                
            elif choice == "5":
                # Volver
                break

def main():
    """Función principal"""
    menu = MigrationMenu()
    menu.run()

if __name__ == "__main__":
    main()
