# Scripts de Migración S1 → S2

Scripts para migrar datos faltantes desde el servidor S1 (MySQL 5.7) hacia S2 (MySQL 8.1 en Azure).

## Configuración

### Servidores
- **S1 (Origen)**: s2.gmetrics.cl - MySQL 5.7 - Base: s2gmetri_prod2
- **S2 (Destino)**: coreprodlegacy.mysql.database.azure.com - MySQL 8.1 - Base: dbgmprod

### Instalación

```bash
pip install -r requirements.txt
```

## Scripts Disponibles

### 1. migration_analysis.py
Analiza las diferencias entre S1 y S2, genera reportes de estado.

```bash
# Ejecutar análisis completo
python migration_analysis.py
```

**Salida:**
- Reporte en consola con resumen
- Archivo CSV con detalles: `migration_report_YYYYMMDD_HHMMSS.csv`
- Lista de tablas que necesitan migración

### 2. migrate_s1_to_s2.py
Ejecuta la migración de datos faltantes.

```bash
# Migración completa (modo prueba)
python migrate_s1_to_s2.py --dry-run

# Migración completa (ejecución real)
python migrate_s1_to_s2.py

# Migrar tablas específicas
python migrate_s1_to_s2.py --tables tabla1 tabla2 tabla3

# Cambiar tamaño de lote
python migrate_s1_to_s2.py --batch-size 500
```

**Parámetros:**
- `--tables`: Lista de tablas específicas a migrar
- `--dry-run`: Modo prueba (no ejecuta cambios)
- `--batch-size`: Tamaño del lote (default: 1000)

## Proceso Recomendado

### 1. Análisis Inicial
```bash
python migration_analysis.py
```
Esto te mostrará:
- Qué tablas existen en cada servidor
- Cuántas filas faltan por migrar
- Lista de tablas que necesitan migración

### 2. Prueba de Migración
```bash
python migrate_s1_to_s2.py --dry-run
```
Ejecuta una simulación sin hacer cambios reales.

### 3. Migración Real
```bash
# Migrar todas las tablas pendientes
python migrate_s1_to_s2.py

# O migrar tablas específicas
python migrate_s1_to_s2.py --tables tabla1 tabla2
```

### 4. Verificación
```bash
python migration_analysis.py
```
Verifica que la migración se completó correctamente.

## Características del Script

### Seguridad
- Usa `INSERT IGNORE` para evitar duplicados
- Transacciones con rollback en caso de error
- Logging detallado de todas las operaciones
- Modo dry-run para pruebas

### Eficiencia
- Migración por lotes (configurable)
- Solo migra datos faltantes (no duplica)
- Usa claves primarias para ordenamiento consistente
- Conexiones optimizadas para ambos servidores

### Monitoreo
- Logs detallados en archivo y consola
- Progreso en tiempo real
- Reportes de estado y errores
- Archivos de log con timestamp

## Archivos Generados

- `migration_YYYYMMDD_HHMMSS.log`: Log detallado de migración
- `migration_report_YYYYMMDD_HHMMSS.csv`: Reporte de análisis

## Solución de Problemas

### Error de Conexión
- Verificar credenciales y conectividad
- Comprobar firewall y SSL para Azure

### Errores de Migración
- Revisar logs detallados
- Verificar estructura de tablas
- Comprobar restricciones de integridad

### Rendimiento Lento
- Reducir `--batch-size`
- Migrar tablas específicas en lugar de todas
- Ejecutar durante horas de menor carga

## Notas Importantes

1. **Backup**: Siempre hacer backup antes de migrar
2. **Horario**: Ejecutar durante horas de menor actividad
3. **Monitoreo**: Supervisar logs durante la ejecución
4. **Verificación**: Siempre verificar resultados con analysis.py

## Ejemplo de Uso Completo

```bash
# 1. Instalar dependencias
pip install -r requirements.txt

# 2. Análisis inicial
python migration_analysis.py

# 3. Prueba de migración
python migrate_s1_to_s2.py --dry-run

# 4. Migración real
python migrate_s1_to_s2.py

# 5. Verificación final
python migration_analysis.py
```
# Migrar tablas pequeñas
python3 migrate_s1_to_s2.py --tables gm_buttons gm_alert_auto_responses_codes login_logs

# Migrar tablas medianas  
python3 migrate_s1_to_s2.py --tables gm_stats_day easy_sybelws_copy --batch-size 500

# Migrar tablas grandes individualmente
python3 migrate_s1_to_s2.py --tables gm_console_pings --batch-size 1000