#!/usr/bin/env python3
"""
Script de análisis para comparar bases de datos S1 y S2
Genera reportes de diferencias y estado de migración
"""

import mysql.connector
import pandas as pd
from datetime import datetime
import logging
import sys

# Configuración de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuración de conexiones (misma que migrate_s1_to_s2.py)
S1_CONFIG = {
    'host': 's2.gmetrics.cl',
    'port': 3306,
    'user': 's2gmetri_user',
    'password': 'Etw7h6sxLPWQ',
    'database': 's2gmetri_prod2',
    'charset': 'utf8mb4'
}

S2_CONFIG = {
    'host': 'coreprodlegacy.mysql.database.azure.com',
    'port': 3306,
    'user': 'adminlegacydev',
    'password': 'fvb8LmY347ToNvsq2sPqgD0E0JBnXUX9',
    'database': 'dbgmprod',
    'charset': 'utf8mb4',
    'ssl_disabled': False
}

class MigrationAnalyzer:
    def __init__(self, use_estimates=False):
        self.s1_conn = None
        self.s2_conn = None
        self.use_estimates = use_estimates
    
    def connect_databases(self):
        """Establece conexiones a ambas bases de datos"""
        try:
            self.s1_conn = mysql.connector.connect(**S1_CONFIG)
            self.s2_conn = mysql.connector.connect(**S2_CONFIG)
            logger.info("Conexiones establecidas exitosamente")
        except mysql.connector.Error as e:
            logger.error(f"Error conectando: {e}")
            raise
    
    def close_connections(self):
        """Cierra las conexiones"""
        if self.s1_conn:
            self.s1_conn.close()
        if self.s2_conn:
            self.s2_conn.close()
    
    def get_table_comparison(self):
        """Compara tablas entre S1 y S2"""
        s1_cursor = self.s1_conn.cursor()
        s2_cursor = self.s2_conn.cursor()
        
        # Obtener tablas de S1 (excluyendo vistas)
        s1_cursor.execute("""
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'BASE TABLE'
        """, (S1_CONFIG['database'],))
        s1_tables = set(table[0] for table in s1_cursor.fetchall())

        # Obtener tablas de S2 (excluyendo vistas)
        s2_cursor.execute("""
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'BASE TABLE'
        """, (S2_CONFIG['database'],))
        s2_tables = set(table[0] for table in s2_cursor.fetchall())
        
        # Análisis
        common_tables = s1_tables & s2_tables
        s1_only = s1_tables - s2_tables
        s2_only = s2_tables - s1_tables
        
        # Contar vistas para información
        s1_cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'VIEW'
        """, (S1_CONFIG['database'],))
        s1_views = s1_cursor.fetchone()[0]

        s2_cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'VIEW'
        """, (S2_CONFIG['database'],))
        s2_views = s2_cursor.fetchone()[0]

        logger.info(f"Tablas en S1: {len(s1_tables)} (+ {s1_views} vistas omitidas)")
        logger.info(f"Tablas en S2: {len(s2_tables)} (+ {s2_views} vistas omitidas)")
        logger.info(f"Tablas comunes: {len(common_tables)}")
        logger.info(f"Solo en S1: {len(s1_only)}")
        logger.info(f"Solo en S2: {len(s2_only)}")
        
        if s1_only:
            logger.warning(f"Tablas solo en S1: {list(s1_only)}")
        if s2_only:
            logger.warning(f"Tablas solo en S2: {list(s2_only)}")
        
        s1_cursor.close()
        s2_cursor.close()
        
        return common_tables, s1_only, s2_only
    
    def get_row_counts_comparison(self, tables):
        """Compara conteos de filas entre S1 y S2 usando consultas optimizadas"""
        comparison_data = []

        # Obtener estadísticas usando el método apropiado
        if self.use_estimates:
            logger.info("Obteniendo estadísticas aproximadas de S1...")
            s1_stats = self.get_table_stats_estimates('s1')
            logger.info("Obteniendo estadísticas aproximadas de S2...")
            s2_stats = self.get_table_stats_estimates('s2')
        else:
            logger.info("Obteniendo conteos reales de S1...")
            s1_stats = self.get_table_stats_bulk('s1')
            logger.info("Obteniendo conteos reales de S2...")
            s2_stats = self.get_table_stats_bulk('s2')

        for table in tables:
            try:
                s1_count = s1_stats.get(table, 0)
                s2_count = s2_stats.get(table, 0)

                difference = s1_count - s2_count
                status = "OK" if difference == 0 else "PENDIENTE" if difference > 0 else "EXTRA_S2"

                comparison_data.append({
                    'tabla': table,
                    's1_filas': s1_count,
                    's2_filas': s2_count,
                    'diferencia': difference,
                    'estado': status
                })

                if len(comparison_data) % 50 == 0:
                    logger.info(f"Procesadas {len(comparison_data)} tablas...")

            except Exception as e:
                logger.error(f"Error comparando tabla {table}: {e}")
                comparison_data.append({
                    'tabla': table,
                    's1_filas': 'ERROR',
                    's2_filas': 'ERROR',
                    'diferencia': 'ERROR',
                    'estado': 'ERROR'
                })

        return comparison_data

    def get_table_stats_bulk(self, server):
        """Obtiene estadísticas de todas las tablas usando COUNT(*) real"""
        conn = self.s1_conn if server == 's1' else self.s2_conn
        db_name = S1_CONFIG['database'] if server == 's1' else S2_CONFIG['database']

        cursor = conn.cursor()

        # Obtener lista de tablas primero
        cursor.execute("""
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """, (db_name,))

        tables = [row[0] for row in cursor.fetchall()]
        stats = {}

        logger.info(f"Contando filas reales en {len(tables)} tablas de {server.upper()}...")

        for i, table_name in enumerate(tables, 1):
            try:
                # Usar COUNT(*) real para obtener conteos exactos
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                actual_count = cursor.fetchone()[0]
                stats[table_name] = actual_count

                # Log de progreso cada 25 tablas
                if i % 25 == 0:
                    logger.info(f"  Procesadas {i}/{len(tables)} tablas en {server.upper()}")

            except Exception as e:
                logger.warning(f"Error contando tabla {table_name} en {server}: {e}")
                stats[table_name] = 0

        cursor.close()
        logger.info(f"✓ Completado conteo de {len(tables)} tablas en {server.upper()}")
        return stats

    def get_table_stats_estimates(self, server):
        """Obtiene estadísticas aproximadas usando information_schema (más rápido pero menos preciso)"""
        conn = self.s1_conn if server == 's1' else self.s2_conn
        db_name = S1_CONFIG['database'] if server == 's1' else S2_CONFIG['database']

        cursor = conn.cursor()
        logger.info(f"Obteniendo estimaciones rápidas de {server.upper()}...")

        # Usar information_schema para obtener estadísticas aproximadas
        cursor.execute("""
            SELECT TABLE_NAME, TABLE_ROWS
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = %s
            AND TABLE_TYPE = 'BASE TABLE'
        """, (db_name,))

        stats = {}
        for table_name, row_count in cursor.fetchall():
            stats[table_name] = row_count or 0

        cursor.close()
        logger.warning(f"⚠️  Usando estimaciones aproximadas para {server.upper()} - pueden ser inexactas")
        return stats
    
    def generate_report(self):
        """Genera reporte completo de análisis"""
        logger.info("Generando reporte de análisis...")
        
        # Comparar tablas
        common_tables, s1_only, s2_only = self.get_table_comparison()
        
        # Comparar conteos de filas
        comparison_data = self.get_row_counts_comparison(common_tables)
        
        # Crear DataFrame para mejor visualización
        df = pd.DataFrame(comparison_data)
        
        # Generar archivo de reporte
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"migration_report_{timestamp}.csv"
        df.to_csv(report_file, index=False)
        
        # Mostrar resumen
        print("\n" + "="*60)
        print("REPORTE DE ANÁLISIS DE MIGRACIÓN")
        print("="*60)
        print(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Tablas analizadas: {len(common_tables)}")
        
        if not df.empty:
            pendientes = df[df['estado'] == 'PENDIENTE']
            ok_tables = df[df['estado'] == 'OK']
            
            print(f"Tablas sincronizadas: {len(ok_tables)}")
            print(f"Tablas con datos pendientes: {len(pendientes)}")
            
            if not pendientes.empty:
                print("\nTABLAS CON DATOS PENDIENTES:")
                print("-" * 40)
                for _, row in pendientes.iterrows():
                    print(f"{row['tabla']}: {row['diferencia']} filas pendientes")
                
                total_pendiente = pendientes['diferencia'].sum()
                print(f"\nTotal de filas pendientes: {total_pendiente}")
        
        print(f"\nReporte detallado guardado en: {report_file}")
        print("="*60)
        
        return df
    
    def get_tables_to_migrate(self):
        """Obtiene lista de tablas que necesitan migración"""
        common_tables, _, _ = self.get_table_comparison()
        comparison_data = self.get_row_counts_comparison(common_tables)
        
        tables_to_migrate = []
        for data in comparison_data:
            if data['estado'] == 'PENDIENTE' and isinstance(data['diferencia'], int) and data['diferencia'] > 0:
                tables_to_migrate.append(data['tabla'])
        
        return tables_to_migrate

def main():
    import argparse

    parser = argparse.ArgumentParser(description='Analizar diferencias entre S1 y S2')
    parser.add_argument('--fast', action='store_true',
                       help='Usar estimaciones rápidas (menos preciso pero más rápido)')
    parser.add_argument('--debug-counts', nargs='+', metavar='TABLE',
                       help='Comparar conteos reales vs estimados para tablas específicas')

    args = parser.parse_args()

    # Si se solicita debug de conteos específicos
    if args.debug_counts:
        import subprocess
        print("🔍 Ejecutando debug de conteos...")
        subprocess.run(['python', 'debug_count_issue.py'] + args.debug_counts)
        return

    analyzer = MigrationAnalyzer(use_estimates=args.fast)

    if args.fast:
        print("⚡ Modo rápido activado - usando estimaciones aproximadas")
        print("   Nota: Los resultados pueden ser inexactos. Para precisión total usa sin --fast")
    else:
        print("🎯 Modo preciso activado - usando conteos reales (puede tomar más tiempo)")

    try:
        analyzer.connect_databases()

        # Generar reporte
        df = analyzer.generate_report()

        # Mostrar tablas que necesitan migración
        tables_to_migrate = analyzer.get_tables_to_migrate()
        if tables_to_migrate:
            print(f"\nTablas que necesitan migración ({len(tables_to_migrate)}):")
            for table in tables_to_migrate:
                print(f"  - {table}")

            print(f"\nPara migrar estas tablas, ejecuta:")
            print(f"python migrate_s1_to_s2.py --tables {' '.join(tables_to_migrate)}")
        else:
            print("\n✅ Todas las tablas están sincronizadas")

    except Exception as e:
        logger.error(f"Error en análisis: {e}")
    finally:
        analyzer.close_connections()

if __name__ == "__main__":
    main()
