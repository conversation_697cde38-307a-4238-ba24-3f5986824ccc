#!/usr/bin/env python3
"""
Script de resumen para mostrar el estado actual de la migración
"""

import pandas as pd
import glob
import os
from datetime import datetime

def show_migration_summary():
    """Muestra un resumen del estado de migración"""
    
    print("=" * 70)
    print("RESUMEN DE MIGRACIÓN S1 → S2")
    print("=" * 70)
    print(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Buscar el reporte más reciente
    csv_files = glob.glob("migration_report_*.csv")
    if not csv_files:
        print("❌ No se encontraron reportes de análisis")
        return
    
    latest_report = max(csv_files, key=os.path.getctime)
    print(f"📊 Usando reporte: {latest_report}")
    
    try:
        df = pd.read_csv(latest_report)
        
        # Estadísticas generales
        total_tables = len(df)
        ok_tables = len(df[df['estado'] == 'OK'])
        pending_tables = len(df[df['estado'] == 'PENDIENTE'])
        extra_s2_tables = len(df[df['estado'] == 'EXTRA_S2'])
        error_tables = len(df[df['estado'] == 'ERROR'])
        
        print(f"📈 ESTADÍSTICAS GENERALES:")
        print(f"   Total de tablas analizadas: {total_tables}")
        print(f"   ✅ Tablas sincronizadas: {ok_tables}")
        print(f"   ⏳ Tablas pendientes: {pending_tables}")
        print(f"   ⚠️  Tablas con datos extra en S2: {extra_s2_tables}")
        print(f"   ❌ Tablas con errores: {error_tables}")
        print()
        
        # Tablas pendientes
        if pending_tables > 0:
            pending_df = df[df['estado'] == 'PENDIENTE'].copy()
            pending_df = pending_df.sort_values('diferencia', ascending=False)
            
            print(f"📋 TABLAS PENDIENTES DE MIGRACIÓN ({pending_tables}):")
            print("-" * 50)
            
            total_pending_rows = 0
            for _, row in pending_df.iterrows():
                filas_pendientes = row['diferencia']
                total_pending_rows += filas_pendientes
                
                # Categorizar por tamaño
                if filas_pendientes > 1000000:
                    size_icon = "🔴"  # Muy grande
                elif filas_pendientes > 10000:
                    size_icon = "🟡"  # Grande
                else:
                    size_icon = "🟢"  # Pequeña
                
                print(f"   {size_icon} {row['tabla']:35} {filas_pendientes:>10,} filas")
            
            print("-" * 50)
            print(f"   📊 TOTAL FILAS PENDIENTES: {total_pending_rows:,}")
            print()
            
            # Recomendaciones
            print("💡 RECOMENDACIONES:")
            
            small_tables = pending_df[pending_df['diferencia'] <= 1000]
            medium_tables = pending_df[(pending_df['diferencia'] > 1000) & (pending_df['diferencia'] <= 100000)]
            large_tables = pending_df[pending_df['diferencia'] > 100000]
            
            if len(small_tables) > 0:
                print(f"   🟢 Migrar primero {len(small_tables)} tablas pequeñas (≤1K filas)")
                small_list = ' '.join(small_tables['tabla'].tolist()[:10])  # Primeras 10
                print(f"      python3 migrate_s1_to_s2.py --tables {small_list}")
                print()
            
            if len(medium_tables) > 0:
                print(f"   🟡 Migrar {len(medium_tables)} tablas medianas (1K-100K filas) con lotes pequeños")
                medium_list = ' '.join(medium_tables['tabla'].tolist()[:5])  # Primeras 5
                print(f"      python3 migrate_s1_to_s2.py --tables {medium_list} --batch-size 500")
                print()
            
            if len(large_tables) > 0:
                print(f"   🔴 Migrar {len(large_tables)} tablas grandes (>100K filas) individualmente")
                for _, row in large_tables.head(3).iterrows():  # Primeras 3
                    print(f"      python3 migrate_s1_to_s2.py --tables {row['tabla']} --batch-size 1000")
                print()
        
        else:
            print("🎉 ¡TODAS LAS TABLAS ESTÁN SINCRONIZADAS!")
            print()
        
        # Archivos de log disponibles
        log_files = glob.glob("migration_*.log")
        if log_files:
            print(f"📝 ARCHIVOS DE LOG DISPONIBLES ({len(log_files)}):")
            for log_file in sorted(log_files, key=os.path.getctime, reverse=True)[:5]:
                size = os.path.getsize(log_file)
                mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                print(f"   📄 {log_file} ({size} bytes, {mtime.strftime('%H:%M:%S')})")
            print()
        
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ Error leyendo el reporte: {e}")

if __name__ == "__main__":
    show_migration_summary()
